'use client'

import { useCallback } from 'react'

import { useToastContext } from '../components'
import { LOGIN_REFERER_URL } from '../constants'
import { useLazyGetLoginUrlQuery } from '../services'
import { userIsAuthLoadingSelector, userIsLoggedInSelector } from '../store'
import { useAppSelector } from '../store/hooks'
import { appLocalStorage, resolveCatchMessage, TCatchMessage } from '../utils'

/**
 * 授权相关 hook
 */
const useAuth = () => {
  const toast = useToastContext()
  const isLoggedIn = useAppSelector(userIsLoggedInSelector)
  const isAuthLoading = useAppSelector(userIsAuthLoadingSelector)
  const [getLoginUrlQuery, { isLoading: isGetLoginUrlLoading }] = useLazyGetLoginUrlQuery()
  
   * 创建登录链接
   * @param callbackUrl 回调地址
   */
  const getLoginUrl = useCallback(
    async (callbackUrl: string) => {
      try {
        const response = await getLoginUrlQuery({
          callbackUrl,
        }).unwrap()

        if (response.web_passport_login_url?.login_url) {
          return response.web_passport_login_url.login_url
        }

        return null
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
        return null
      }
    },
    [getLoginUrlQuery, toast],
  )

  /**
   * 创建注册链接
   * @param callbackUrl 回调地址
   */
  const getRegisterUrl = useCallback(
    async (callbackUrl: string) => {
      try {
        const response = await getLoginUrlQuery({
          callbackUrl,
        }).unwrap()

        if (response.web_passport_login_url?.register_url) {
          return response.web_passport_login_url.register_url
        }

        return null
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
        return null
      }
    },
    [getLoginUrlQuery, toast],
  )

  /**
   * 跳转登录链接
   * @param callbackUrl 回调地址
   */
  const redirectLoginUrl = useCallback(
    async (callbackUrl?: string) => {
      const url = callbackUrl || window.location.href
      appLocalStorage.setItemSync(LOGIN_REFERER_URL, url)
      const response = await getLoginUrl(url)

      if (response) {
        window.location.href = response
      }
    },
    [getLoginUrl],
  )

  /**
   * 跳转注册链接
   * @param callbackUrl 回调地址
   */
  const redirectRegisterUrl = useCallback(
    async (callbackUrl?: string) => {
      const url = callbackUrl || window.location.href
      appLocalStorage.setItemSync(LOGIN_REFERER_URL, url)
      const response = await getRegisterUrl(url)

      if (response) {
        window.location.href = response
      }
    },
    [getRegisterUrl],
  )

  return {
    isLoggedIn,
    isAuthLoading,
    getLoginUrl,
    getRegisterUrl,
    isGetLoginUrlLoading,
    redirectLoginUrl,
    redirectRegisterUrl,
  }
}

export default useAuth
