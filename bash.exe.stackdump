Stack trace:
Frame         Function      Args
0007FFFF9F90  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E90) msys-2.0.dll+0x1FE8E
0007FFFF9F90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA268) msys-2.0.dll+0x67F9
0007FFFF9F90  000210046832 (000210286019, 0007FFFF9E48, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F90  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F90  000210068E24 (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA270  00021006A225 (0007FFFF9FA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD92840000 ntdll.dll
7FFD90A90000 KERNEL32.DLL
7FFD8FD10000 KERNELBASE.dll
7FFD911A0000 USER32.dll
7FFD8F970000 win32u.dll
7FFD90B60000 GDI32.dll
7FFD8FA50000 gdi32full.dll
7FFD8F9A0000 msvcp_win.dll
7FFD90290000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD90890000 advapi32.dll
7FFD92720000 msvcrt.dll
7FFD925E0000 sechost.dll
7FFD90950000 RPCRT4.dll
7FFD8EE60000 CRYPTBASE.DLL
7FFD90530000 bcryptPrimitives.dll
7FFD92530000 IMM32.DLL
