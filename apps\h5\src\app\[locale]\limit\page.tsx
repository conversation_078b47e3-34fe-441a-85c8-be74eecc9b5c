'use client'
import { useCallback, useState } from 'react'
import { generateOSSUrl } from '@ninebot/core'
import { useCountDown } from 'ahooks'
import { Button } from 'antd-mobile'

const Page = () => {
  const [disabled, setDisabled] = useState(true)
  const [countdown] = useCountDown({
    leftTime: 20 * 1000,
    onEnd: () => {
      setDisabled(false)
    },
  })

  const handleBack = useCallback(() => {
    if (disabled) {
      return
    }

    window.location.href = '/'
  }, [disabled])

  return (
    <div
      className="flex h-screen flex-col items-center justify-center bg-cover"
      style={{
        backgroundImage: `url(${generateOSSUrl('/images/limit-bg.png')})`,
      }}>
      <p className="mb-9 px-[24px] text-center font-[14px] leading-[1.2] text-white">
        当前访问人数较多，建议您等待倒计时结束后再次尝试访问
      </p>
      <div>
        <Button className="nb-button w-[180px]" color="primary" onClick={handleBack}>
          {disabled ? <>（{Math.ceil(countdown / 1000)} s）后尝试访问</> : '尝试访问'}
        </Button>
      </div>
    </div>
  )
}

export default Page
